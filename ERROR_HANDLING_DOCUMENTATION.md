# Global API Error Handling System

This Laravel 12 application now includes a comprehensive global error handling system that provides consistent JSON responses for all API errors.

## Features

✅ **Consistent Error Format**: All API errors return a standardized JSON structure
✅ **Custom Exception Classes**: Easy-to-use custom exceptions for common scenarios
✅ **Helper Traits**: Convenient methods for controllers to return consistent responses
✅ **Security**: Sensitive information is hidden in production environments
✅ **Debugging**: Detailed error information in development mode
✅ **Comprehensive Coverage**: Handles all common error types

## Error Response Format

All API errors return a consistent JSON structure:

```json
{
    "success": false,
    "message": "Human-readable error message",
    "error": "Error type",
    "code": "ERROR_CODE",
    "errors": {} // Optional: validation errors
}
```

## Supported Error Types

### 1. Authentication Errors (401)
```json
{
    "success": false,
    "message": "Authentication required",
    "error": "Unauthenticated",
    "code": "UNAUTHENTICATED"
}
```

### 2. Authorization Errors (403)
```json
{
    "success": false,
    "message": "Insufficient permissions",
    "error": "Forbidden",
    "code": "FORBIDDEN"
}
```

### 3. Validation Errors (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "error": "Validation Error",
    "code": "VALIDATION_ERROR",
    "errors": {
        "email": ["The email field is required."],
        "password": ["The password must be at least 8 characters."]
    }
}
```

### 4. Not Found Errors (404)
```json
{
    "success": false,
    "message": "Resource not found",
    "error": "Not Found",
    "code": "RESOURCE_NOT_FOUND"
}
```

### 5. Method Not Allowed (405)
```json
{
    "success": false,
    "message": "Method not allowed",
    "error": "Method Not Allowed",
    "code": "METHOD_NOT_ALLOWED"
}
```

### 6. Rate Limiting (429)
```json
{
    "success": false,
    "message": "Too many requests",
    "error": "Rate Limit Exceeded",
    "code": "RATE_LIMIT_EXCEEDED"
}
```

### 7. Server Errors (500)
```json
{
    "success": false,
    "message": "An unexpected error occurred",
    "error": "Internal Server Error",
    "code": "INTERNAL_SERVER_ERROR"
}
```

## Implementation Components

### 1. ApiExceptionHandler (`app/Exceptions/ApiExceptionHandler.php`)
- Main exception handler that catches and formats all API errors
- Handles different exception types with appropriate responses
- Logs errors for debugging while hiding sensitive information

### 2. ApiException (`app/Exceptions/ApiException.php`)
- Custom exception class for API-specific errors
- Provides static methods for common error types
- Includes context data and debug information

### 3. ApiResponseTrait (`app/Traits/ApiResponseTrait.php`)
- Helper trait for controllers
- Provides convenient methods for returning consistent responses
- Includes success, error, and specialized response methods

### 4. Updated Middleware
- `CheckRole` and `CheckPermission` middleware return consistent error formats
- Proper error codes and messages for authentication/authorization failures

## Usage Examples

### In Controllers

```php
use App\Traits\ApiResponseTrait;

class UserController extends Controller
{
    use ApiResponseTrait;

    public function show($id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->notFoundResponse('User not found');
        }
        
        return $this->successResponse($user, 'User retrieved successfully');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users'
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        // Create user logic...
        return $this->createdResponse($user, 'User created successfully');
    }
}
```

### Throwing Custom Exceptions

```php
// Throw specific exceptions
$this->throwNotFound('User not found');
$this->throwUnauthorized('Invalid credentials');
$this->throwForbidden('Access denied');
$this->throwBadRequest('Invalid input data');

// Or use the generic method
$this->throwApiException('Custom error message', 'CUSTOM_ERROR_CODE', 400);
```

### Available Response Methods

```php
// Success responses
$this->successResponse($data, 'Success message', 200);
$this->createdResponse($data, 'Created successfully');
$this->noContentResponse();

// Error responses
$this->errorResponse('Error message', 'ERROR_CODE', 500);
$this->validationErrorResponse($errors);
$this->notFoundResponse('Resource not found');
$this->unauthorizedResponse('Unauthorized');
$this->forbiddenResponse('Forbidden');
$this->conflictResponse('Conflict');
$this->serverErrorResponse('Server error');

// Specialized responses
$this->paginatedResponse($paginatedData, 'Data retrieved');
```

## Configuration

The error handler is automatically registered in `bootstrap/app.php`:

```php
->withExceptions(function (Exceptions $exceptions) {
    $exceptions->render(function (Throwable $e, $request) {
        if ($request->is('api/*') || $request->expectsJson()) {
            return app(\App\Exceptions\ApiExceptionHandler::class)->handle($e, $request);
        }
    });
})
```

## Security Features

- **Production Mode**: Sensitive error details are hidden
- **Debug Mode**: Full error information including stack traces
- **Logging**: All errors are logged with context information
- **No Information Leakage**: Database errors and internal details are sanitized

## Testing

The system includes comprehensive tests (`tests/Feature/ApiErrorHandlingTest.php`) that verify:

- ✅ Validation error formatting
- ✅ Authentication error handling
- ✅ Authorization error handling
- ✅ Not found error handling
- ✅ Method not allowed handling
- ✅ Custom exception handling
- ✅ Production vs development error exposure
- ✅ Debug information inclusion

## Test Routes (Development Only)

For testing purposes, the following routes are available in non-production environments:

- `GET /api/test/success` - Test successful response
- `GET /api/test/api-exception` - Test custom API exception
- `GET /api/test/not-found` - Test not found exception
- `GET /api/test/unauthorized` - Test unauthorized exception
- `GET /api/test/forbidden` - Test forbidden exception
- `GET /api/test/server-error` - Test server error
- `GET /api/test/validation-error` - Test validation error response
- `GET /api/test/generic-exception` - Test generic exception handling

## Benefits

1. **Consistency**: All API errors follow the same format
2. **Developer Experience**: Easy-to-use helper methods and exceptions
3. **Security**: Proper error information exposure control
4. **Debugging**: Comprehensive logging and debug information
5. **Maintainability**: Centralized error handling logic
6. **Testing**: Comprehensive test coverage ensures reliability

The global error handling system is now fully implemented and tested, providing a robust foundation for your Laravel API backend!
