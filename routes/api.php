<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TestController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Test routes for error handling (only in non-production environments)
if (!app()->environment('production')) {
    Route::prefix('test')->group(function () {
        Route::get('/success', [TestController::class, 'success']);
        Route::get('/api-exception', [TestController::class, 'testApiException']);
        Route::get('/not-found', [TestController::class, 'testNotFound']);
        Route::get('/unauthorized', [TestController::class, 'testUnauthorized']);
        Route::get('/forbidden', [TestController::class, 'testForbidden']);
        Route::get('/server-error', [TestController::class, 'testServerError']);
        Route::get('/validation-error', [TestController::class, 'testValidationError']);
        Route::get('/paginated', [TestController::class, 'testPaginatedResponse']);
        Route::get('/created', [TestController::class, 'testCreatedResponse']);
        Route::get('/generic-exception', [TestController::class, 'testGenericException']);
        Route::get('/database-exception', [TestController::class, 'testDatabaseException']);
        Route::get('/model-not-found', [TestController::class, 'testModelNotFound']);
    });
}

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout-all', [AuthController::class, 'logoutAll']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'profile']);

    // Basic authenticated user route
    Route::get('/user', function (Request $request) {
        return response()->json([
            'success' => true,
            'data' => [
                'user' => $request->user()->load('roles.permissions')
            ]
        ]);
    });

    // Dashboard route (all authenticated users)
    Route::get('/dashboard', function (Request $request) {
        return response()->json([
            'success' => true,
            'message' => 'Welcome to dashboard!',
            'data' => [
                'user' => $request->user()->only(['id', 'name', 'email']),
                'roles' => $request->user()->getRoleNames(),
                'permissions' => $request->user()->getAllPermissions()->pluck('name')
            ]
        ]);
    })->middleware('permission:view dashboard');

    // Admin routes (require admin or super-admin role)
    Route::middleware('role:admin,super-admin')->prefix('admin')->group(function () {
        Route::get('/users', function (Request $request) {
            return response()->json([
                'success' => true,
                'data' => [
                    'users' => \App\Models\User::with('roles')->get()
                ]
            ]);
        })->middleware('permission:view users');

        Route::get('/settings', function (Request $request) {
            return response()->json([
                'success' => true,
                'message' => 'Admin settings accessed',
                'data' => [
                    'settings' => [
                        'app_name' => config('app.name'),
                        'app_env' => config('app.env'),
                        'user_count' => \App\Models\User::count()
                    ]
                ]
            ]);
        })->middleware('permission:manage settings');
    });

    // Manager routes (require manager, admin, or super-admin role)
    Route::middleware('role:manager,admin,super-admin')->prefix('manager')->group(function () {
        Route::get('/reports', function (Request $request) {
            return response()->json([
                'success' => true,
                'message' => 'Manager reports accessed',
                'data' => [
                    'reports' => [
                        'total_users' => \App\Models\User::count(),
                        'active_tokens' => \Laravel\Sanctum\PersonalAccessToken::count(),
                        'roles_count' => \Spatie\Permission\Models\Role::count()
                    ]
                ]
            ]);
        })->middleware('permission:view reports');
    });

    // Super Admin only routes
    Route::middleware('role:super-admin')->prefix('super-admin')->group(function () {
        Route::get('/system-info', function (Request $request) {
            return response()->json([
                'success' => true,
                'message' => 'System information accessed',
                'data' => [
                    'php_version' => PHP_VERSION,
                    'laravel_version' => app()->version(),
                    'database_connection' => config('database.default'),
                    'cache_driver' => config('cache.default'),
                    'queue_driver' => config('queue.default')
                ]
            ]);
        });

        Route::delete('/users/{user}', function (\App\Models\User $user) {
            if ($user->hasRole('super-admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete super admin user'
                ], 403);
            }

            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
        })->middleware('permission:delete users');
    });
});
