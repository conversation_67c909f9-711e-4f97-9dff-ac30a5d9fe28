<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

class ApiException extends Exception
{
    protected string $errorCode;
    protected int $statusCode;
    protected array $context;

    public function __construct(
        string $message = 'An error occurred',
        string $errorCode = 'API_ERROR',
        int $statusCode = 500,
        array $context = [],
        ?Exception $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        
        $this->errorCode = $errorCode;
        $this->statusCode = $statusCode;
        $this->context = $context;
    }

    /**
     * Create a new API exception for bad request
     */
    public static function badRequest(string $message = 'Bad request', array $context = []): self
    {
        return new self($message, 'BAD_REQUEST', 400, $context);
    }

    /**
     * Create a new API exception for unauthorized access
     */
    public static function unauthorized(string $message = 'Unauthorized', array $context = []): self
    {
        return new self($message, 'UNAUTHORIZED', 401, $context);
    }

    /**
     * Create a new API exception for forbidden access
     */
    public static function forbidden(string $message = 'Forbidden', array $context = []): self
    {
        return new self($message, 'FORBIDDEN', 403, $context);
    }

    /**
     * Create a new API exception for not found
     */
    public static function notFound(string $message = 'Resource not found', array $context = []): self
    {
        return new self($message, 'NOT_FOUND', 404, $context);
    }

    /**
     * Create a new API exception for conflict
     */
    public static function conflict(string $message = 'Conflict', array $context = []): self
    {
        return new self($message, 'CONFLICT', 409, $context);
    }

    /**
     * Create a new API exception for unprocessable entity
     */
    public static function unprocessableEntity(string $message = 'Unprocessable entity', array $context = []): self
    {
        return new self($message, 'UNPROCESSABLE_ENTITY', 422, $context);
    }

    /**
     * Create a new API exception for internal server error
     */
    public static function serverError(string $message = 'Internal server error', array $context = []): self
    {
        return new self($message, 'INTERNAL_SERVER_ERROR', 500, $context);
    }

    /**
     * Create a new API exception for service unavailable
     */
    public static function serviceUnavailable(string $message = 'Service unavailable', array $context = []): self
    {
        return new self($message, 'SERVICE_UNAVAILABLE', 503, $context);
    }

    /**
     * Get the error code
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * Get the HTTP status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the context data
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Convert the exception to a JSON response
     */
    public function toResponse(): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $this->getMessage(),
            'error' => $this->getErrorCode(),
            'code' => $this->getErrorCode()
        ];

        // Add context data if available
        if (!empty($this->context)) {
            $response['context'] = $this->context;
        }

        // Add debug information in debug mode
        if (config('app.debug')) {
            $response['debug'] = [
                'file' => $this->getFile(),
                'line' => $this->getLine(),
                'trace' => $this->getTraceAsString()
            ];
        }

        return response()->json($response, $this->statusCode);
    }
}
