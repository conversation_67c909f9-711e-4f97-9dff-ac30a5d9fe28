<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Illuminate\Database\QueryException;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use App\Exceptions\ApiException;

class ApiExceptionHandler
{
    /**
     * Handle API exceptions and return consistent JSON responses
     */
    public function handle(Throwable $exception, Request $request): JsonResponse
    {
        // Log the exception for debugging (except for expected exceptions)
        if (!$this->shouldntReport($exception)) {
            logger()->error('API Exception: ' . $exception->getMessage(), [
                'exception' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => $request->user()?->id,
            ]);
        }

        return match (true) {
            // Custom API exceptions
            $exception instanceof ApiException => $exception->toResponse(),

            // Authentication errors
            $exception instanceof AuthenticationException => $this->handleAuthenticationException($exception),

            // Authorization errors (including Spatie permission errors)
            $exception instanceof AuthorizationException,
            $exception instanceof UnauthorizedException => $this->handleAuthorizationException($exception),
            
            // Validation errors
            $exception instanceof ValidationException => $this->handleValidationException($exception),
            
            // Model not found errors
            $exception instanceof ModelNotFoundException => $this->handleModelNotFoundException($exception),
            
            // HTTP exceptions
            $exception instanceof NotFoundHttpException => $this->handleNotFoundException($exception),
            $exception instanceof MethodNotAllowedHttpException => $this->handleMethodNotAllowedException($exception),
            $exception instanceof TooManyRequestsHttpException,
            $exception instanceof ThrottleRequestsException => $this->handleTooManyRequestsException($exception),
            $exception instanceof HttpException => $this->handleHttpException($exception),
            
            // Database errors
            $exception instanceof QueryException => $this->handleDatabaseException($exception),
            
            // Default handler for other exceptions
            default => $this->handleGenericException($exception),
        };
    }

    /**
     * Handle authentication exceptions
     */
    protected function handleAuthenticationException(AuthenticationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Authentication required',
            'error' => 'Unauthenticated',
            'code' => 'UNAUTHENTICATED'
        ], 401);
    }

    /**
     * Handle authorization exceptions
     */
    protected function handleAuthorizationException(Throwable $exception): JsonResponse
    {
        $message = 'Access denied';
        
        if ($exception instanceof UnauthorizedException) {
            $message = 'Insufficient permissions';
        }

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => 'Forbidden',
            'code' => 'FORBIDDEN'
        ], 403);
    }

    /**
     * Handle validation exceptions
     */
    protected function handleValidationException(ValidationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'error' => 'Validation Error',
            'code' => 'VALIDATION_ERROR',
            'errors' => $exception->errors()
        ], 422);
    }

    /**
     * Handle model not found exceptions
     */
    protected function handleModelNotFoundException(ModelNotFoundException $exception): JsonResponse
    {
        $model = class_basename($exception->getModel());
        
        return response()->json([
            'success' => false,
            'message' => "{$model} not found",
            'error' => 'Resource Not Found',
            'code' => 'RESOURCE_NOT_FOUND'
        ], 404);
    }

    /**
     * Handle not found exceptions
     */
    protected function handleNotFoundException(NotFoundHttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Endpoint not found',
            'error' => 'Not Found',
            'code' => 'ENDPOINT_NOT_FOUND'
        ], 404);
    }

    /**
     * Handle method not allowed exceptions
     */
    protected function handleMethodNotAllowedException(MethodNotAllowedHttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Method not allowed',
            'error' => 'Method Not Allowed',
            'code' => 'METHOD_NOT_ALLOWED'
        ], 405);
    }

    /**
     * Handle too many requests exceptions
     */
    protected function handleTooManyRequestsException(Throwable $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Too many requests',
            'error' => 'Rate Limit Exceeded',
            'code' => 'RATE_LIMIT_EXCEEDED'
        ], 429);
    }

    /**
     * Handle HTTP exceptions
     */
    protected function handleHttpException(HttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $exception->getMessage() ?: 'HTTP Error',
            'error' => 'HTTP Exception',
            'code' => 'HTTP_ERROR'
        ], $exception->getStatusCode());
    }

    /**
     * Handle database exceptions
     */
    protected function handleDatabaseException(QueryException $exception): JsonResponse
    {
        // Don't expose sensitive database information in production
        $message = config('app.debug') 
            ? 'Database error: ' . $exception->getMessage()
            : 'A database error occurred';

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => 'Database Error',
            'code' => 'DATABASE_ERROR'
        ], 500);
    }

    /**
     * Handle generic exceptions
     */
    protected function handleGenericException(Throwable $exception): JsonResponse
    {
        // Don't expose sensitive information in production
        $message = config('app.debug') 
            ? $exception->getMessage()
            : 'An unexpected error occurred';

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => 'Internal Server Error',
            'code' => 'INTERNAL_SERVER_ERROR'
        ], 500);
    }

    /**
     * Determine if the exception should not be reported
     */
    protected function shouldntReport(Throwable $exception): bool
    {
        $dontReport = [
            AuthenticationException::class,
            AuthorizationException::class,
            UnauthorizedException::class,
            ValidationException::class,
            ModelNotFoundException::class,
            NotFoundHttpException::class,
            MethodNotAllowedHttpException::class,
            TooManyRequestsHttpException::class,
            ThrottleRequestsException::class,
        ];

        foreach ($dontReport as $type) {
            if ($exception instanceof $type) {
                return true;
            }
        }

        return false;
    }
}
