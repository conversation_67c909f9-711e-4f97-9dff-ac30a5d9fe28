<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use App\Exceptions\ApiException;

trait ApiResponseTrait
{
    /**
     * Return a success JSON response
     */
    protected function successResponse(
        mixed $data = null,
        string $message = 'Success',
        int $statusCode = 200
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error JSON response
     */
    protected function errorResponse(
        string $message = 'An error occurred',
        string $errorCode = 'ERROR',
        int $statusCode = 500,
        array $errors = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            // 'error' => $errorCode,
            // 'code' => $errorCode,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Validation failed'
    ): JsonResponse {
        // Flatten validation errors to get first error message for each field
        $flattenedErrors = [];
        foreach ($errors as $field => $messages) {
            $flattenedErrors[$field] = is_array($messages) ? $messages[0] : $messages;
        }
        // dd((object)$flattenedErrors);

        return $this->errorResponse($message, 'VALIDATION_ERROR', 422, $flattenedErrors);
    }

    /**
     * Return a not found error response
     */
    protected function notFoundResponse(string $message = 'Resource not found'): JsonResponse
    {
        return $this->errorResponse($message, 'NOT_FOUND', 404);
    }

    /**
     * Return an unauthorized error response
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->errorResponse($message, 'UNAUTHORIZED', 401);
    }

    /**
     * Return a forbidden error response
     */
    protected function forbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return $this->errorResponse($message, 'FORBIDDEN', 403);
    }

    /**
     * Return a conflict error response
     */
    protected function conflictResponse(string $message = 'Conflict'): JsonResponse
    {
        return $this->errorResponse($message, 'CONFLICT', 409);
    }

    /**
     * Return a server error response
     */
    protected function serverErrorResponse(string $message = 'Internal server error'): JsonResponse
    {
        return $this->errorResponse($message, 'INTERNAL_SERVER_ERROR', 500);
    }

    /**
     * Throw a custom API exception
     */
    protected function throwApiException(
        string $message,
        string $errorCode = 'API_ERROR',
        int $statusCode = 500,
        array $context = []
    ): never {
        throw new ApiException($message, $errorCode, $statusCode, $context);
    }

    /**
     * Throw a bad request exception
     */
    protected function throwBadRequest(string $message = 'Bad request', array $context = []): never
    {
        throw ApiException::badRequest($message, $context);
    }

    /**
     * Throw an unauthorized exception
     */
    protected function throwUnauthorized(string $message = 'Unauthorized', array $context = []): never
    {
        throw ApiException::unauthorized($message, $context);
    }

    /**
     * Throw a forbidden exception
     */
    protected function throwForbidden(string $message = 'Forbidden', array $context = []): never
    {
        throw ApiException::forbidden($message, $context);
    }

    /**
     * Throw a not found exception
     */
    protected function throwNotFound(string $message = 'Resource not found', array $context = []): never
    {
        throw ApiException::notFound($message, $context);
    }

    /**
     * Throw a conflict exception
     */
    protected function throwConflict(string $message = 'Conflict', array $context = []): never
    {
        throw ApiException::conflict($message, $context);
    }

    /**
     * Throw an unprocessable entity exception
     */
    protected function throwUnprocessableEntity(string $message = 'Unprocessable entity', array $context = []): never
    {
        throw ApiException::unprocessableEntity($message, $context);
    }

    /**
     * Throw a server error exception
     */
    protected function throwServerError(string $message = 'Internal server error', array $context = []): never
    {
        throw ApiException::serverError($message, $context);
    }

    /**
     * Return a paginated response
     */
    protected function paginatedResponse(
        $paginatedData,
        string $message = 'Data retrieved successfully'
    ): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $paginatedData->items(),
            'pagination' => [
                'current_page' => $paginatedData->currentPage(),
                'last_page' => $paginatedData->lastPage(),
                'per_page' => $paginatedData->perPage(),
                'total' => $paginatedData->total(),
                'from' => $paginatedData->firstItem(),
                'to' => $paginatedData->lastItem(),
                'has_more_pages' => $paginatedData->hasMorePages(),
            ],
        ]);
    }

    /**
     * Return a created response
     */
    protected function createdResponse(
        mixed $data = null,
        string $message = 'Resource created successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Return a no content response
     */
    protected function noContentResponse(): JsonResponse
    {
        return response()->json(null, 204);
    }
}
