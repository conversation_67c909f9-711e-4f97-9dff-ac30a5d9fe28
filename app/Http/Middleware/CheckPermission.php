<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
                'error' => 'Unauthenticated',
                'code' => 'UNAUTHENTICATED'
            ], 401);
        }

        if (!$request->user()->hasAnyPermission($permissions)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions. Required permissions: ' . implode(', ', $permissions),
                'error' => 'Forbidden',
                'code' => 'FORBIDDEN'
            ], 403);
        }

        return $next($request);
    }
}
