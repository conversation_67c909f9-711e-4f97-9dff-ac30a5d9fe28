<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TestController extends Controller
{
    use ApiResponseTrait;

    /**
     * Test successful response
     */
    public function success(): JsonResponse
    {
        return $this->successResponse([
            'message' => 'This is a successful response',
            'timestamp' => now(),
            'version' => '1.0.0'
        ], 'API is working correctly');
    }

    /**
     * Test custom API exception
     */
    public function testApiException(): never
    {
        $this->throwBadRequest('This is a test API exception', [
            'field' => 'test_field',
            'value' => 'invalid_value'
        ]);
    }

    /**
     * Test not found exception
     */
    public function testNotFound(): never
    {
        $this->throwNotFound('The requested resource was not found');
    }

    /**
     * Test unauthorized exception
     */
    public function testUnauthorized(): never
    {
        $this->throwUnauthorized('You are not authorized to access this resource');
    }

    /**
     * Test forbidden exception
     */
    public function testForbidden(): never
    {
        $this->throwForbidden('You do not have permission to perform this action');
    }

    /**
     * Test server error exception
     */
    public function testServerError(): never
    {
        $this->throwServerError('An internal server error occurred');
    }

    /**
     * Test validation error response
     */
    public function testValidationError(): JsonResponse
    {
        return $this->validationErrorResponse([
            'name' => ['The name field is required.'],
            'email' => ['The email field must be a valid email address.'],
            'password' => ['The password field must be at least 8 characters.']
        ], 'The given data was invalid');
    }

    /**
     * Test paginated response
     */
    public function testPaginatedResponse(): JsonResponse
    {
        $users = User::paginate(10);
        return $this->paginatedResponse($users, 'Users retrieved successfully');
    }

    /**
     * Test created response
     */
    public function testCreatedResponse(): JsonResponse
    {
        $user = [
            'id' => 1,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'created_at' => now()
        ];

        return $this->createdResponse($user, 'User created successfully');
    }

    /**
     * Test generic exception (will be caught by global handler)
     */
    public function testGenericException(): never
    {
        throw new \Exception('This is a generic exception that should be handled by the global error handler');
    }

    /**
     * Test database exception simulation
     */
    public function testDatabaseException(): never
    {
        // This will throw a QueryException when trying to find a non-existent table
        \Illuminate\Support\Facades\DB::table('non_existent_table')->get();
    }

    /**
     * Test model not found exception
     */
    public function testModelNotFound(): never
    {
        User::findOrFail(99999); // This will throw ModelNotFoundException
    }
}
