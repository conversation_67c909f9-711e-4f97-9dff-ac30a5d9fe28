<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ValidationErrorFormatTest extends TestCase
{
    use RefreshDatabase;

    public function test_validation_errors_are_flattened_in_registration()
    {
        // Create a user with the same email to trigger unique validation
        User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/register', [
            'name' => '',
            'email' => '<EMAIL>', // This should trigger unique validation
            'password' => '123',
            'password_confirmation' => '456',
        ]);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors' => [
                        'name',
                        'email',
                        'password'
                    ]
                ]);

        // Check that errors are flattened (string values, not arrays)
        $responseData = $response->json();
        
        $this->assertIsString($responseData['errors']['name']);
        $this->assertIsString($responseData['errors']['email']);
        $this->assertIsString($responseData['errors']['password']);
        
        // Verify specific error messages
        $this->assertEquals('The name field is required.', $responseData['errors']['name']);
        $this->assertEquals('The email has already been taken.', $responseData['errors']['email']);
        $this->assertStringContains('password', strtolower($responseData['errors']['password']));
    }

    public function test_validation_errors_are_flattened_in_login()
    {
        $response = $this->postJson('/api/login', [
            'email' => 'invalid-email',
            'password' => '',
        ]);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'errors' => [
                        'email',
                        'password'
                    ]
                ]);

        // Check that errors are flattened (string values, not arrays)
        $responseData = $response->json();
        
        $this->assertIsString($responseData['errors']['email']);
        $this->assertIsString($responseData['errors']['password']);
        
        // Verify specific error messages
        $this->assertEquals('The email field must be a valid email address.', $responseData['errors']['email']);
        $this->assertEquals('The password field is required.', $responseData['errors']['password']);
    }

    public function test_validation_error_format_matches_expected_structure()
    {
        $response = $this->postJson('/api/register', [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
        ]);

        $response->assertStatus(422);
        
        $responseData = $response->json();
        
        // Verify the overall structure
        $this->assertFalse($responseData['success']);
        $this->assertEquals('Validation failed', $responseData['message']);
        $this->assertIsArray($responseData['errors']);
        
        // Verify that each error is a string, not an array
        foreach ($responseData['errors'] as $field => $error) {
            $this->assertIsString($error, "Error for field '{$field}' should be a string, not an array");
        }
        
        // Verify expected error structure matches your requirement:
        // "errors": {
        //     "email": "The email has already been taken."
        // }
        $this->assertArrayHasKey('email', $responseData['errors']);
        $this->assertStringContains('email', strtolower($responseData['errors']['email']));
    }

    public function test_successful_registration_does_not_have_errors()
    {
        $response = $this->postJson('/api/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user',
                        'token',
                        'token_type'
                    ]
                ])
                ->assertJsonMissing(['errors']);

        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertArrayNotHasKey('errors', $responseData);
    }

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic roles and permissions for testing
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
    }
}
