<?php

namespace Tests\Feature;

use App\Models\User;
use App\Exceptions\ApiException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiErrorHandlingTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_returns_consistent_error_format_for_validation_errors()
    {
        $response = $this->postJson('/api/register', [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
        ]);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code',
                    'errors' => [
                        'name',
                        'email',
                        'password'
                    ]
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'VALIDATION_ERROR'
                ]);
    }

    public function test_api_returns_consistent_error_format_for_authentication_errors()
    {
        $response = $this->getJson('/api/profile');

        $response->assertStatus(401)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'UNAUTHENTICATED'
                ]);
    }

    public function test_api_returns_consistent_error_format_for_authorization_errors()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/admin/users');

        $response->assertStatus(403)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'FORBIDDEN'
                ]);
    }

    public function test_api_returns_consistent_error_format_for_not_found_errors()
    {
        $response = $this->getJson('/api/non-existent-endpoint');

        $response->assertStatus(404)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'ENDPOINT_NOT_FOUND'
                ]);
    }

    public function test_api_returns_consistent_error_format_for_method_not_allowed()
    {
        $response = $this->putJson('/api/register');

        $response->assertStatus(405)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'METHOD_NOT_ALLOWED'
                ]);
    }

    public function test_api_returns_consistent_error_format_for_invalid_credentials()
    {
        $user = User::factory()->create();

        $response = $this->postJson('/api/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        $response->assertStatus(401)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => false
                ]);
    }

    public function test_custom_api_exception_returns_proper_format()
    {
        // Create a test route that throws a custom API exception
        \Illuminate\Support\Facades\Route::get('/test-api-exception', function () {
            throw ApiException::badRequest('This is a test error', ['test' => 'context']);
        });

        $response = $this->getJson('/test-api-exception');

        $response->assertStatus(400)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code',
                    'context'
                ])
                ->assertJson([
                    'success' => false,
                    'message' => 'This is a test error',
                    'code' => 'BAD_REQUEST',
                    'context' => ['test' => 'context']
                ]);
    }

    public function test_model_not_found_returns_proper_format()
    {
        // Create a test route that throws ModelNotFoundException
        \Illuminate\Support\Facades\Route::get('/api/test-model-not-found', function () {
            User::findOrFail(99999); // This will throw ModelNotFoundException
        });

        $response = $this->getJson('/api/test-model-not-found');

        $response->assertStatus(404)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'code' => 'RESOURCE_NOT_FOUND'
                ]);
    }

    public function test_error_responses_do_not_expose_sensitive_information_in_production()
    {
        // Temporarily set app to production mode
        config(['app.debug' => false]);

        // Create a test route that throws a generic exception
        \Illuminate\Support\Facades\Route::get('/test-generic-exception', function () {
            throw new \Exception('Sensitive database connection string: mysql://user:pass@host/db');
        });

        $response = $this->getJson('/test-generic-exception');

        $response->assertStatus(500)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code'
                ])
                ->assertJson([
                    'success' => false,
                    'message' => 'An unexpected error occurred',
                    'code' => 'INTERNAL_SERVER_ERROR'
                ]);

        // Ensure sensitive information is not exposed
        $response->assertJsonMissing([
            'message' => 'Sensitive database connection string: mysql://user:pass@host/db'
        ]);
    }

    public function test_error_responses_include_debug_information_in_debug_mode()
    {
        // Ensure app is in debug mode
        config(['app.debug' => true]);

        // Create a test route that throws a custom API exception
        \Illuminate\Support\Facades\Route::get('/test-debug-exception', function () {
            throw ApiException::serverError('Debug test error');
        });

        $response = $this->getJson('/test-debug-exception');

        $response->assertStatus(500)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'error',
                    'code',
                    'debug' => [
                        'file',
                        'line',
                        'trace'
                    ]
                ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic roles and permissions for testing
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
    }
}
