<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // for Mobile Authentication
        $middleware->statefulApi();

        // Register custom middleware aliases
        $middleware->alias([
            'role' => \App\Http\Middleware\CheckRole::class,
            'permission' => \App\Http\Middleware\CheckPermission::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Handle API exceptions with consistent JSON responses
        $exceptions->render(function (Throwable $e, $request) {
            // Only handle API requests (requests that expect JSON)
            if ($request->is('api/*') || $request->expectsJson()) {
                return app(\App\Exceptions\ApiExceptionHandler::class)->handle($e, $request);
            }
        });
    })->create();
