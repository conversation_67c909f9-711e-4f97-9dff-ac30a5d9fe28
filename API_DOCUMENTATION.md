# Laravel API Authentication with <PERSON><PERSON> Roles & Permissions

This Laravel 12 application provides a complete API authentication system using Laravel Sanctum and <PERSON><PERSON> Permission for role-based access control.

## Features

- User registration and login
- JWT-like token authentication via Laravel Sanctum
- Role-based access control (RBAC)
- Permission-based access control
- Token refresh and logout functionality
- Comprehensive API endpoints with proper middleware protection

## Default Roles & Permissions

### Roles
- **super-admin**: Full system access
- **admin**: Administrative access (most permissions)
- **manager**: Limited management access
- **user**: Basic user access

### Permissions
- User management: `view users`, `create users`, `edit users`, `delete users`
- Role management: `view roles`, `create roles`, `edit roles`, `delete roles`
- Permission management: `view permissions`, `create permissions`, `edit permissions`, `delete permissions`
- General: `view dashboard`, `manage settings`, `view reports`, `export data`

## API Endpoints

### Public Endpoints

#### Register User
```
POST /api/register
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
}
```

#### Login User
```
POST /api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Test API
```
GET /api/test
```

### Protected Endpoints (Require Authentication)

All protected endpoints require the `Authorization: Bearer {token}` header.

#### User Profile
```
GET /api/profile
Authorization: Bearer {token}
```

#### Dashboard (Requires 'view dashboard' permission)
```
GET /api/dashboard
Authorization: Bearer {token}
```

#### Logout
```
POST /api/logout
Authorization: Bearer {token}
```

#### Logout from All Devices
```
POST /api/logout-all
Authorization: Bearer {token}
```

#### Refresh Token
```
POST /api/refresh
Authorization: Bearer {token}
```

### Admin Routes (Require admin or super-admin role)

#### View All Users (Requires 'view users' permission)
```
GET /api/admin/users
Authorization: Bearer {token}
```

#### Admin Settings (Requires 'manage settings' permission)
```
GET /api/admin/settings
Authorization: Bearer {token}
```

### Manager Routes (Require manager, admin, or super-admin role)

#### View Reports (Requires 'view reports' permission)
```
GET /api/manager/reports
Authorization: Bearer {token}
```

### Super Admin Routes (Require super-admin role only)

#### System Information
```
GET /api/super-admin/system-info
Authorization: Bearer {token}
```

#### Delete User (Requires 'delete users' permission)
```
DELETE /api/super-admin/users/{user_id}
Authorization: Bearer {token}
```

## Default Users

After running the seeders, these users will be available:

- **Super Admin**: <EMAIL> / password123
- **Admin**: <EMAIL> / password123
- **Manager**: <EMAIL> / password123
- **Test User**: <EMAIL> / password (assigned 'user' role)

## Setup Instructions

1. Install dependencies:
```bash
composer install
```

2. Run migrations:
```bash
php artisan migrate
```

3. Seed roles, permissions, and users:
```bash
php artisan db:seed
```

4. Start the development server:
```bash
php artisan serve
```

## Testing

Run the authentication tests:
```bash
php artisan test tests/Feature/AuthenticationTest.php
```

## Usage Examples

### 1. Register a new user
```bash
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

### 2. Login and get token
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. Access protected endpoint
```bash
curl -X GET http://localhost:8000/api/dashboard \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Error Responses

The API returns consistent error responses:

```json
{
    "success": false,
    "message": "Error description",
    "errors": {} // Validation errors when applicable
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created (registration)
- `401`: Unauthenticated
- `403`: Forbidden (insufficient permissions)
- `422`: Validation errors
- `500`: Server error

## Security Features

- Password hashing using bcrypt
- Token-based authentication
- Role and permission-based access control
- CORS configuration for API access
- Input validation and sanitization
- Secure token storage and management
